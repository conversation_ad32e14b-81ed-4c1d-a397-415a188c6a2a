PROJECT = 'MQTT_GPS'
VERSION = '1.4.0'
PRODUCT_KEY = 'lBQKLcBp5kZ2PT2uphmzqjUcQ7wf6jya'
require 'cc'
require 'ble'
require 'ntp'
require 'log'
require 'led'
require 'sys'
require 'sms'
require 'net'
require 'misc'
require 'patch'
require 'netLed'
require 'socket'
require 'usermqtt'
require 'update'
require 'audio'
vars = require('variables')
my_utils = require('my_utils')
local commands = require('commands')
local PinModule = require('PinModule') -- Import the PinModule
local SensorModule = require('SensorModule') -- Import the SensorModule
local SmsModule = require('SmsModule') -- Import the SmsModule
local LogModule = require('LogModule') -- Import the enhanced logging module
local VOLTAGE_CHECK_INTERVAL = 10000  -- Check every 10 seconds
local last_voltage = 0

LOG_LEVEL = log.LOGLEVEL_INFO -- Default log level for production
-- Initialize BLE Module
local success, err = pcall(ble.BLE_Init)
if not success then
    log.error("BLE", "Failed to initialize BLE: " .. (err or "Unknown error"))
    -- Handle the error, possibly by retrying or shutting down
    sys.exit()
end
-- Setup GPIO pins using PinModule with error handling
local success, err = pcall(PinModule.setupPins)
if not success then
    log.error("PinModule", "Failed to setup pins: " .. (err or "Unknown error"))
    -- Handle the error, like retrying or shutting down the system
    sys.exit() -- Exit the system to prevent further undefined behavior
end

local function setLogLevel(level)
    log.setLevel(level)
end

local function updateCurrentTime()
    vars.currentTime = vars.currentTime + 1 -- Increment the current time by one second
end

sys.timerLoopStart(updateCurrentTime, 1000) -- 1000 milliseconds (1 second)

rtos.set_trace(1, 2) -- Set logs to be printed via UART1

function upload_data()
    log.info("USER", "TIMER UPLOAD")
    vars.upload_flag = true
end

-- Initialize the SmsModule to handle SMS callbacks with error handling
local success, err = pcall(SmsModule.init)
if not success then
    log.error("SmsModule", "Failed to initialize SmsModule: " .. (err or "Unknown error"))
    -- Handle the error accordingly
end

-- SMS Callback function
local function smsCallback(num, data, datetime)
    local success, err = pcall(SmsModule.smsCallback, num, data, datetime)
    if not success then
        log.error("SmsModule", "Error in SMS callback: " .. (err or "Unknown error"))
        -- Handle the error
    end
end
-- Function to send SMS to multiple phone numbers safely
local function sendLicenseNotify()
    local message = "Tani license duussan bna. SMS uilchilgee haagdaj bna"
    log.warn("MQTT", "License notify via sms.")

    -- Send SMS to phone_number1 if it's valid (not nil and not empty)
    if vars.phone_number1 and vars.phone_number1 ~= "" then
        local success, err = pcall(sms.send, vars.phone_number1, message)
        if not success then
            log.error("sendLicenseNotify", "Failed to send SMS to " .. vars.phone_number1 .. ": " .. err)
        end
    end

    -- Send SMS to phone_number2 if it's valid
    if vars.phone_number2 and vars.phone_number2 ~= "" then
        local success, err = pcall(sms.send, vars.phone_number2, message)
        if not success then
            log.error("sendLicenseNotify", "Failed to send SMS to " .. vars.phone_number2 .. ": " .. err)
        end
    end

    -- Send SMS to phone_number3 if it's valid
    if vars.phone_number3 and vars.phone_number3 ~= "" then
        local success, err = pcall(sms.send, vars.phone_number3, message)
        if not success then
            log.error("sendLicenseNotify", "Failed to send SMS to " .. vars.phone_number3 .. ": " .. err)
        end
    end
end
local function handlePinStateChange(pin, lastState, pinName, mqttMsg)
    local success, currentState = pcall(PinModule.readPinState, pinName)
    if not success then
        log.error("PinModule", "Failed to read pin state: " .. (currentState or "Unknown error"))
        return lastState -- Return the last known state to avoid undefined behavior
    end

    if lastState ~= currentState then
        if LOG_LEVEL <= log.LOGLEVEL_INFO then
            log.info("alarmCallback", pinName .. " Change")
        end
        local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
        if not success then
            log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
        end
    end
    return currentState
end

local function handleS2State(s2_state)
    if vars.asa_state == true and s2_state == 0 then
        vars.relay1_state = 0
        local success, err = pcall(PinModule.relayControl, "Relay1", 0)
        if not success then
            log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
        end
    end
    if vars.as_wait_s2_falling == true and s2_state == 0 then
        vars.as_wait_s2_falling = false
        if LOG_LEVEL <= log.LOGLEVEL_INFO then
            log.info("user", "s2 falling target")
        end
        vars.relay2_state = 1
        local success, err = pcall(PinModule.relayControl, "Relay2", 1)
        if not success then
            log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
        end
        sys.wait(3000)
        vars.relay2_state = 0
        local success, err = pcall(PinModule.relayControl, "Relay2", 0)
        if not success then
            log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
        end
    end
end

local function alarmCallback()
    local success, s1_state = pcall(PinModule.readPinState, "S1")
    if not success then
        log.error("PinModule", "Failed to read S1 pin state: " .. (s1_state or "Unknown error"))
        s1_state = 0 -- Default to safe state
    end

    local success, s2_state = pcall(PinModule.readPinState, "S2")
    if not success then
        log.error("PinModule", "Failed to read S2 pin state: " .. (s2_state or "Unknown error"))
        s2_state = 0 -- Default to safe state
    end

    while true do
        s1_state = handlePinStateChange("S1", s1_state, "S1", "S1 Change")
        s2_state = handlePinStateChange("S2", s2_state, "S2", "S2 Change")
        handleS2State(s2_state)
        -- Removed handleTemperatureControl() call
        sys.wait(1000) -- Wait 1000ms
    end
end

sys.taskInit(alarmCallback) -- Run detection thread

sys.taskInit(function()
    local debounceTime = 2 -- Time in seconds to ignore repeated commands
    local lastLockTime = 0
    local lastUnlockTime = 0
    local lastMirrorTime = 0 -- Added for debouncing the mirror command
    local mirrorPressCount = 0 -- Count mirror button presses for restart detection
    local mirrorPressWindow = 5 -- Time window in seconds to count mirror presses (increased from 2 to 4)
    local mirrorPressThreshold = 4 -- Number of presses needed within window to restart
    local mirrorWindowStartTime = 0 -- When we started counting mirror presses
    local lastSpecificCommandTime = {} -- For debouncing specific commands
    local commandCounter = {}
    local triggerCount = 3 -- Trigger threshold for command execution

    while true do
        -- Initialize UART1
        local success, err = pcall(uart.setup, 1, 9600, 8, uart.PAR_NONE, uart.STOP_1, 1, 0, 0, 1)
        if not success then
            log.error("UART", "Failed to setup UART1: " .. (err or "Unknown error"))
        end

        -- Initialize UART2 (if not already set up)
        local success2, err2 = pcall(uart.setup, 2, 9600, 8, uart.PAR_NONE, uart.STOP_1, 1, 0, 0, 1)
        if not success2 then
            log.error("UART", "Failed to setup UART2: " .. (err2 or "Unknown error"))
        end

        -- Read data from UART1
        local uart1_data = uart.read(1, "*l")

        -- Check if mirror press window has expired
        if mirrorPressCount > 0 and (vars.currentTime - mirrorWindowStartTime > mirrorPressWindow) then
            -- Reset counter if window expired
            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                log.info("user", "Mirror press window expired, resetting counter from " .. mirrorPressCount)
            end
            mirrorPressCount = 0
        end

        if string.len(uart1_data) ~= 0 then
            uart1_data = uart1_data:match("^%s*(.-)%s*$")
            print("Received UART Data: " .. uart1_data)

            -- Process UART1 commands based on specific fixed identifiers (4, 8, 2, 1)
            local identifier = uart1_data:sub(9, 9) -- Extracting the 9th character to identify the button/action
            if identifier == "8" and (vars.currentTime - lastLockTime > debounceTime) then
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("user", "Lock command detected and executed")
                end
                local success, err = pcall(commands.lockCommand)
                if not success then
                    log.error("commands", "Failed to execute lock command: " .. (err or "Unknown error"))
                end
                lastLockTime = vars.currentTime
            elseif identifier == "4" and (vars.currentTime - lastUnlockTime > debounceTime) then
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("user", "Unlock command received and executed")
                end
                local success, err = pcall(commands.unlockCommand)
                if not success then
                    log.error("commands", "Failed to execute unlock command: " .. (err or "Unknown error"))
                end
                lastUnlockTime = vars.currentTime
            elseif identifier == "2" then
                -- Start a new window if this is the first press
                if mirrorPressCount == 0 then
                    mirrorWindowStartTime = vars.currentTime
                end

                -- Increment the press counter
                mirrorPressCount = mirrorPressCount + 1

                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("user", "Mirror button press detected, count: " .. mirrorPressCount ..
                             " within " .. (vars.currentTime - mirrorWindowStartTime) .. " seconds (need " ..
                             mirrorPressThreshold .. " presses within " .. mirrorPressWindow .. " seconds to restart)")
                end

                -- Check if we've reached the threshold for restart
                if mirrorPressCount >= mirrorPressThreshold then
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("user", "Mirror button pressed " .. mirrorPressCount ..
                                 " times within " .. mirrorPressWindow .. " seconds, restarting module")
                    end

                    -- Reset the counter
                    mirrorPressCount = 0

                    -- Restart the module
                    local r = 1 -- Use the appropriate restart argument based on your platform
                    local success, err = pcall(function()
                        sys.restart(r)
                    end)

                    if not success then
                        log.error("commands", "Failed to restart module: " .. (err or "Unknown error"))
                    end
                end

                -- Normal mirror button press handling
                if vars.currentTime - lastMirrorTime > debounceTime then
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("user", "Mirror command from remote received")
                    end
                    local success, err = pcall(commands.mirrorCommand)
                    if not success then
                        log.error("commands", "Failed to execute mirror command: " .. (err or "Unknown error"))
                    end
                    lastMirrorTime = vars.currentTime
                end

            elseif identifier == "1" then
                if not lastSpecificCommandTime[uart1_data] or
                    (vars.currentTime - lastSpecificCommandTime[uart1_data] > debounceTime) then
                    commandCounter[uart1_data] = (commandCounter[uart1_data] or 0) + 1

                    if commandCounter[uart1_data] >= triggerCount then
                        local success, err = pcall(commands.testCommand)
                        if not success then
                            log.error("commands", "Failed to execute asCommand: " .. (err or "Unknown error"))
                        else
                            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                                log.info("asCommand", "Executed action when trigger count met")
                            end
                        end
                        commandCounter[uart1_data] = 0
                        lastSpecificCommandTime[uart1_data] = vars.currentTime
                    end
                end
            end
        end

        -- Read data from UART2
        local uart2_data = uart.read(2, "*l")
        if string.len(uart2_data) ~= 0 then
            uart2_data = uart2_data:match("^%s*(.-)%s*$")
            if uart2_data == "test" then
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("UART2", "Test command received on UART2, executing asCommand")
                end
                local success, err = pcall(commands.testCommand)
                if not success then
                    log.error("commands", "Failed to execute asCommand from UART2: " .. (err or "Unknown error"))
                end
            end
        end

        sys.wait(200) -- Delay 200ms
    end
end)

-- Function to process MQTT messages with error handling
local function processMqttMessage(msg)
    if LOG_LEVEL <= log.LOGLEVEL_INFO then
        log.info("main", "MQTT Message: ", msg)
    end

    -- Check if the message contains a setserver command embedded in JSON
    local setserverCommand = string.match(msg, '"setserver%s+([^"]+)"')
    if setserverCommand then
        log.info("main", "Extracted setserver command from JSON: " .. setserverCommand)
        vars.mqtt_data = "setserver " .. setserverCommand
        return
    end

    -- Check if the message is a direct setserver command (special case)
    if string.find(msg, "setserver%s+") then
        log.info("main", "Direct setserver command detected in message")
        vars.mqtt_data = msg
        return
    end

    local tjsondata, result, errinfo = json.decode(msg)
    if not result then
        if LOG_LEVEL <= log.LOGLEVEL_ERROR then
            log.error("Decode Json", "Decode Fail: " .. (errinfo or "Unknown error"))
        end
        
        -- Try to extract command from malformed JSON as a fallback
        local command = string.match(msg, '"command"%s*:%s*"([^"]+)"')
        if command then
            log.info("main", "Extracted command from malformed JSON: " .. command)
            vars.mqtt_data = command
            return
        end

        -- Try to extract unitel command from malformed JSON (e.g., {"id":"123", "unitel:88392933 2000"})
        local unitel_command = string.match(msg, '"(unitel:%d%d%d%d%d%d%d%d %d+)"')
        if unitel_command then
            log.info("main", "Extracted unitel command from malformed JSON: " .. unitel_command)
            vars.mqtt_data = unitel_command
            return
        end
        
        return
    end

    if tjsondata["id"] ~= usermqtt.clientID() then
        if LOG_LEVEL <= log.LOGLEVEL_WARN then
            log.warn("main", "ID mismatch: Expected " .. usermqtt.clientID() .. " Got " .. tjsondata["id"])
        end
        return
    end

    local command = tjsondata["command"]
    local timer = tjsondata["timer"]
    local temperature = tjsondata["temp"]

    if command == nil then
        if LOG_LEVEL <= log.LOGLEVEL_WARN then
            log.warn("main", "Command is nil, returning")
        end
        return
    end

    if LOG_LEVEL <= log.LOGLEVEL_INFO then
        log.info("USER", "Command: ", command)
    end

    -- Handle command and timer logic
    if tonumber(command) ~= nil then
        vars.upload_flag = false
        local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
        if not success then
            log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
        end
        sys.timerStop(upload_data)

        if tonumber(command) ~= 0 then
            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                log.info("USER", "Turn on Timer: ", tonumber(command), " S")
            end
            sys.timerLoopStart(upload_data, tonumber(command) * 1000)
        end
    end

    if temperature ~= nil then
        local as_target_temp1 = tonumber(string.sub(temperature, 0, string.find(temperature, "x") - 1))
        local untar_target_temp1 = tonumber(string.sub(temperature, string.find(temperature, "x") + 1))

        if as_target_temp1 ~= nil and untar_target_temp1 ~= nil then
            vars.as_target_temp = as_target_temp1
            vars.untar_target_temp = untar_target_temp1
            vars.temp_target_mode = "mqtt"
            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                log.info("target temp:", vars.as_target_temp, "~", vars.untar_target_temp, ">", vars.temp_target_mode)
            end
        else
            vars.temp_target_mode = ""
            if LOG_LEVEL <= log.LOGLEVEL_WARN then
                log.warn("target temp:", "config fail>", as_target_temp1, "|", untar_target_temp1)
            end
        end
    end

    if timer == nil then
        vars.mqtt_data = command
    else
        if tonumber(timer) ~= nil then
            local hours, minutes = math.modf(tonumber(timer))
            minutes = minutes * 100
            table.insert(vars.timers_queue, {
                payload = command,
                type = 0,
                hour = hours,
                minute = minutes
            })
            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                log.info("MQTT Timer Set: ", command, ",", hours, ":", minutes)
            end
        end
    end
end
-- Function to monitor GPS status and play warning when GPS becomes unavailable
local function monitorGPS()
    -- Check if GPS module is available using SensorModule
    if not SensorModule.isGPSAvailable() then
        -- GPS module not available on this device
        if not vars.gps_module_warning_shown then
            log.warn("GPSMonitor", "GPS module not available on this device")
            vars.gps_module_warning_shown = true
        end
        return
    end

    -- Get current GPS status using SensorModule
    local lat, lng, speed = SensorModule.readGPS()
    local current_gps_status = (lat ~= "0 N" and lng ~= "0 E")

    -- Check if GPS status has changed
    if vars.last_gps_status ~= current_gps_status then
        if current_gps_status then
            -- GPS has become available
            log.info("GPSMonitor", "GPS signal acquired")
            log.info("GPSMonitor", "Location: " .. lat .. ", " .. lng .. ", Speed: " .. speed)
            vars.gps_warned = false  -- Reset the warning flag
        else
            -- GPS has become unavailable
            log.warn("GPSMonitor", "GPS signal lost")

            -- Play warning sound if we haven't warned yet
            if not vars.gps_warned and vars.sound_flag then
                log.info("GPSMonitor", "Playing GPS error warning sound")
                -- Use pcall to safely handle any errors during audio playback
                local success, err = pcall(function()
                    audio.play(4, "FILE", "/lua/gpserror.mp3", 7)
                end)

                if not success then
                    log.error("GPSMonitor", "Failed to play GPS error sound: " .. (err or "Unknown error"))
                end

                vars.gps_warned = true  -- Set the flag to avoid repeated warnings
            end
        end

        -- Update the last GPS status
        vars.last_gps_status = current_gps_status
    end
end

local function monitorVoltage()
    -- Get new voltage reading
    local new_voltage = tonumber(SensorModule.readVoltage()) or 0

    -- Skip invalid readings
    if new_voltage == 0 then
        log.error("VoltageMonitor", "Invalid voltage reading")
        return
    end

    -- Calculate voltage difference using configurable threshold
    local voltage_diff = math.abs(new_voltage - last_voltage)

    -- Check if we should send MQTT update
    if last_voltage > 0 and voltage_diff >= vars.voltage_threshold then
        log.info("VoltageMonitor", string.format("Significant voltage change: %.2fV to %.2fV", last_voltage, new_voltage))

        -- Only send notification if the voltage_notify_flag is enabled
        if vars.voltage_notify_flag then
            -- Use pcall to safely handle any errors during MQTT send
            sys.taskInit(function()
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("VoltageMonitor", "Failed to send MQTT: " .. (err or "Unknown error"))
                end

                -- If we have a primary phone number and notification is enabled, send SMS
                if vars.phone_number1 and vars.phone_number1 ~= "" then
                    local message = string.format("Voltage alert: %.2fV (changed by %.2fV)", new_voltage, voltage_diff)
                    local success, err = pcall(sms.send, vars.phone_number1, message)
                    if not success then
                        log.error("VoltageMonitor", "Failed to send SMS: " .. (err or "Unknown error"))
                    else
                        log.info("VoltageMonitor", "SMS notification sent")
                    end
                end
            end)
        else
            log.info("VoltageMonitor", "Notification skipped (disabled by flag)")
        end
    end

    -- Update last voltage
    last_voltage = new_voltage

    -- Also check GPS status while we're at it
    monitorGPS()
end
-- Main loop with error handling
sys.taskInit(function()
    -- Initialize the enhanced logging module first for better error tracking
    local success, err = pcall(LogModule.init)
    if not success then
        log.error("LogModule", "Failed to initialize LogModule: " .. (err or "Unknown error"))
    end

    local success, err = pcall(ril.request, "AT+RNDISCALL=0,1") -- Disable USB network card function
    if not success then
        log.error("RIL", "Failed to disable USB network card: " .. (err or "Unknown error"))
    end

    local success, err = pcall(pmd.ldoset, 15, pmd.LDO_VMMC) -- Enable GPS voltage domain VMMC 15-level voltage output Vout = 3.177V
    if not success then
        log.error("PMD", "Failed to set LDO_VMMC: " .. (err or "Unknown error"))
    end

    local success, err = pcall(SensorModule.init) -- Initialize sensors
    if not success then
        log.error("SensorModule", "Failed to initialize sensors: " .. (err or "Unknown error"))
    end

    local success, err = pcall(pmd.ldoset, 2, pmd.LDO_VLCD) -- Enable LCD voltage domain, Vout = 1.828V
    if not success then
        log.error("PMD", "Failed to set LDO_VLCD: " .. (err or "Unknown error"))
    end

    sms.setNewSmsCb(smsCallback) -- Add SMS processing function

    -- Create the /user_dir directory using our robust helper function
    local dirCreated = my_utils.createDirectory("/user_dir")
    if not dirCreated then
        log.warn("FileSystem", "Could not create /user_dir directory, but will attempt to use it anyway")
    end

    -- Read phone numbers and BLE configurations
    if my_utils.fileExists("/user_dir/phone1.txt") then
        vars.phone_number1 = io.readFile("/user_dir/phone1.txt")
    end
    if my_utils.fileExists("/user_dir/phone2.txt") then
        vars.phone_number2 = io.readFile("/user_dir/phone2.txt")
    end
    if my_utils.fileExists("/user_dir/phone3.txt") then
        vars.phone_number3 = io.readFile("/user_dir/phone3.txt")
    end

    -- Load voltage offset
    if my_utils.fileExists("/user_dir/volt_offset.txt") then
        local content = io.readFile("/user_dir/volt_offset.txt")
        vars.voltage_offset = tonumber(content) or 0
        log.info("VoltageMonitor", string.format("Loaded voltage offset: %.2f", vars.voltage_offset))
    end

    -- Load voltage threshold
    if my_utils.fileExists("/user_dir/volt_threshold.txt") then
        local content = io.readFile("/user_dir/volt_threshold.txt")
        vars.voltage_threshold = tonumber(content) or 0.5
        log.info("VoltageMonitor", string.format("Loaded voltage threshold: %.2f", vars.voltage_threshold))
    end

    -- Load voltage notification flag
    if my_utils.fileExists("/user_dir/volt_notify.txt") then
        local content = io.readFile("/user_dir/volt_notify.txt")
        if content == "true" then
            vars.voltage_notify_flag = true
        else
            vars.voltage_notify_flag = false
        end
        log.info("VoltageMonitor", "Loaded voltage notification flag: " .. tostring(vars.voltage_notify_flag))
    else
        vars.voltage_notify_flag = false
        local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_notify.txt", tostring(vars.voltage_notify_flag))
        if not success then
            log.error("FileUtils", "Failed to write voltage notify flag to file: " .. (err or "Unknown error"))
        end
    end

    if my_utils.fileExists("/user_dir/alram.txt") then
        local content = io.readFile("/user_dir/alram.txt")
        if content == "true" then
            vars.alarm_state = true
        else
            vars.alarm_state = false
        end
    else
        vars.alarm_state = false
        local success, err = pcall(my_utils.writeToFile, "/user_dir/alram.txt", tostring(vars.alarm_state))
        if not success then
            log.error("FileUtils", "Failed to write alarm state to file: " .. (err or "Unknown error"))
        end
    end
    if my_utils.fileExists("/user_dir/keystate.txt") then
        local content = io.readFile("/user_dir/keystate.txt")
        if content == "true" then
            vars.key_state = true
        else
            vars.key_state = false
        end
    else
        vars.key_state = false
        local success, err = pcall(my_utils.writeToFile, "/user_dir/keystate.txt", tostring(vars.key_state))
        if not success then
            log.error("FileUtils", "Failed to write key state to file: " .. (err or "Unknown error"))
        end
    end
    if my_utils.fileExists("/user_dir/sound_flag.txt") then
        local content = io.readFile("/user_dir/sound_flag.txt")
        if content == "true" then
            vars.sound_flag = true
        else
            vars.sound_flag = false
        end
    else
        vars.sound_flag = true
        local success, err = pcall(my_utils.writeToFile, "/user_dir/sound_flag.txt", tostring(vars.sound_flag))
        if not success then
            log.error("FileUtils", "Failed to write sound flag to file: " .. (err or "Unknown error"))
        end
    end

    -- Load isLicensed flag
    if my_utils.fileExists("/user_dir/license.txt") then
        local content = io.readFile("/user_dir/license.txt")
        if content == "true" then
            vars.isLicensed = true
        else
            vars.isLicensed = false
        end
        log.info("License", "Loaded license status: " .. tostring(vars.isLicensed))
    else
        vars.isLicensed = true  -- Default to licensed
        local success, err = pcall(my_utils.writeToFile, "/user_dir/license.txt", tostring(vars.isLicensed))
        if not success then
            log.error("FileUtils", "Failed to write license status to file: " .. (err or "Unknown error"))
        end
    end

    -- Load auto_shutdown_enabled flag
    if my_utils.fileExists("/user_dir/auto_shutdown.txt") then
        local content = io.readFile("/user_dir/auto_shutdown.txt")
        if content == "true" then
            vars.auto_shutdown_enabled = true
        else
            vars.auto_shutdown_enabled = false
        end
        log.info("AutoShutdown", "Auto-shutdown feature is " .. (vars.auto_shutdown_enabled and "enabled" or "disabled"))
    else
        vars.auto_shutdown_enabled = true  -- Default to enabled
        local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown.txt", tostring(vars.auto_shutdown_enabled))
        if not success then
            log.error("FileUtils", "Failed to write auto-shutdown flag to file: " .. (err or "Unknown error"))
        end
    end

    -- Load auto_shutdown_minutes
    if my_utils.fileExists("/user_dir/auto_shutdown_minutes.txt") then
        local content = io.readFile("/user_dir/auto_shutdown_minutes.txt")
        local minutes = tonumber(content)
        if minutes and minutes > 0 then
            vars.auto_shutdown_minutes = minutes
            vars.auto_shutdown_time = vars.auto_shutdown_minutes * 60 * 1000  -- Convert minutes to milliseconds
            log.info("AutoShutdown", string.format("Auto-shutdown timer set to %d minutes", vars.auto_shutdown_minutes))
        end
    else
        -- Default is already set in variables.lua (30 minutes)
        local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown_minutes.txt", tostring(vars.auto_shutdown_minutes))
        if not success then
            log.error("FileUtils", "Failed to write auto-shutdown minutes to file: " .. (err or "Unknown error"))
        end
    end

    -- Load Geely Atlas mode flag
    if my_utils.fileExists("/user_dir/geely_atlas_mode.txt") then
        local content = io.readFile("/user_dir/geely_atlas_mode.txt")
        if content == "true" then
            vars.geely_atlas_mode = true
        else
            vars.geely_atlas_mode = false
        end
        log.info("VehicleMode", "Geely Atlas mode is " .. (vars.geely_atlas_mode and "enabled" or "disabled"))
    else
        vars.geely_atlas_mode = false  -- Default to disabled
        local success, err = pcall(my_utils.writeToFile, "/user_dir/geely_atlas_mode.txt", tostring(vars.geely_atlas_mode))
        if not success then
            log.error("FileUtils", "Failed to write Geely Atlas mode flag to file: " .. (err or "Unknown error"))
        end
    end

    -- Initialize GPS status flags
    vars.gps_warned = false  -- Start with no warning
    vars.gps_module_warning_shown = false  -- Flag to avoid repeated warnings about missing GPS module

    -- Check if GPS module is available using SensorModule
    if SensorModule.isGPSAvailable() then
        -- Try to get initial GPS status
        local lat, lng, speed = SensorModule.readGPS()
        -- If we got valid coordinates, GPS is fixed
        vars.last_gps_status = (lat ~= "0 N" and lng ~= "0 E")
        log.info("GPSMonitor", "Initial GPS status: " .. (vars.last_gps_status and "Fixed" or "Not fixed"))
    else
        vars.last_gps_status = false
        log.warn("GPSMonitor", "GPS module not available on this device")
        vars.gps_module_warning_shown = true
    end

    if LOG_LEVEL <= log.LOGLEVEL_INFO then
        log.info("Phone number1", vars.phone_number1)
        log.info("Phone number2", vars.phone_number2)
        log.info("Phone number3", vars.phone_number3)
        log.info("Alarm state", vars.alarm_state)
        log.info("Key state", vars.key_state)
    end

    ntp.timeSync()

    -- Add the voltage monitoring initialization here
    sys.timerLoopStart(monitorVoltage, VOLTAGE_CHECK_INTERVAL)

    -- Add a dedicated GPS monitoring timer (check every 5 seconds)
    sys.timerLoopStart(monitorGPS, 5000)

    -- Function to load timing parameters
    local function loadTimingParameters()
        -- Define the parameters to load
        local params = {
            {"lock_press", "lock_press_duration", 1000},
            {"unlock_press", "unlock_press_duration", 1000},
            {"lock_wait", "lock_wait_duration", 2000},
            {"unlock_wait", "unlock_wait_duration", 1000},
            {"between_press", "between_press_duration", 1000},
            {"remote_start", "remote_start_duration", 4000},
            {"mirror", "mirror_duration", 3000}
        }

        -- Load each parameter
        for _, param in ipairs(params) do
            local file_name = param[1]
            local var_name = param[2]
            local default_value = param[3]

            if my_utils.fileExists("/user_dir/" .. file_name .. "_duration.txt") then
                local content = io.readFile("/user_dir/" .. file_name .. "_duration.txt")
                local value = tonumber(content)
                if value then
                    vars[var_name] = value
                    log.info("Timing", var_name .. " loaded: " .. value .. "ms")
                else
                    vars[var_name] = default_value
                    log.warn("Timing", "Invalid value in " .. file_name .. "_duration.txt, using default: " .. default_value .. "ms")
                end
            else
                vars[var_name] = default_value
                log.info("Timing", var_name .. " using default: " .. default_value .. "ms")
            end
        end
    end

    -- Call this function during initialization
    loadTimingParameters()

    -- Before your existing while true loop
    while true do
        if vars.upload_flag == true then
            vars.upload_flag = false
            if pin_state == 0 then
                vars.upload_switch = true
            end
            if vars.upload_switch == true then
                if pin_state == 1 then
                    vars.upload_switch = false
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            end
        end

        for index, dat in pairs(vars.timers_queue) do
            local success, tm = pcall(misc.getClock)
            if not success then
                log.error("misc", "Failed to get clock: " .. (tm or "Unknown error"))
                tm = {
                    hour = 0,
                    min = 0
                } -- Default to safe time
            end

            if math.abs(dat.hour - tm.hour) < 1 and math.abs(dat.minute - tm.min) < 1 then
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("Timer Target")
                end
                if dat.type == 1 then
                    vars.sms_data = dat.payload
                else
                    vars.mqtt_data = dat.payload
                end
                table.remove(vars.timers_queue, index)
            end
        end
        if vars.sms_data ~= nil then
            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                log.info("sms data", vars.sms_data)
            end
            if string.find(vars.sms_data, "TT") ~= nil then
                local timer = string.sub(vars.sms_data, string.find(vars.sms_data, "TT") + 2)
                local command = string.sub(vars.sms_data, 0, string.find(vars.sms_data, "TT") - 1)
                if tonumber(timer) ~= nil then
                    local hours, minutes = math.modf(tonumber(timer))
                    minutes = minutes * 100
                    table.insert(vars.timers_queue, {
                        payload = command,
                        type = 1,
                        hour = hours,
                        minute = minutes
                    })
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("SMS Timer Set: ", command, ",", hours, ":", minutes)
                    end
                end
            elseif string.find(vars.sms_data, "temp") ~= nil then
                local as_target_temp1 = tonumber(string.sub(vars.sms_data, string.find(vars.sms_data, "temp") + 4,
                    string.find(vars.sms_data, "x") - 1))
                local untar_target_temp1 = tonumber(string.sub(vars.sms_data, string.find(vars.sms_data, "x") + 1))

                if as_target_temp1 ~= nil and untar_target_temp1 ~= nil then
                    vars.as_target_temp = as_target_temp1
                    vars.untar_target_temp = untar_target_temp1
                    vars.temp_target_mode = "sms"
                    vars.as_target_temp_loop = vars.as_target_temp
                    vars.untar_target_temp_loop = vars.untar_target_temp
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("target temp:", vars.as_target_temp, "~", vars.untar_target_temp, ">",
                            vars.temp_target_mode)
                    end
                else
                    vars.temp_target_mode = ""
                    if LOG_LEVEL <= log.LOGLEVEL_WARN then
                        log.warn("target temp:", "config fail>", as_target_temp1, "|", untar_target_temp1)
                    end
                end
            else
                -- Handle various SMS commands
                if vars.sms_data == "untar" then
                    -- Execute the untarCommand and handle any errors
                    local success, err = pcall(commands.untarCommand, 2000)
                    if not success then
                        log.error("commands", "Failed to execute untar command: " .. (err or "Unknown error"))
                    end

                    -- Parameters for multiple read attempts
                    local max_tries = 3 -- Maximum number of attempts
                    local interval = 5000 -- Interval between attempts in milliseconds (5 seconds)
                    local voltage_threshold = 13.5 -- Voltage threshold indicating successful operation
                    local voltage = 0
                    local successful = false -- Flag to indicate success

                    -- Function to safely read and convert voltage to a number
                    local function getVoltage()
                        local v = SensorModule.readVoltage()
                        v = tonumber(v)
                        if not v then
                            log.error("Voltage", "Invalid voltage reading")
                            return 0 -- Default to 0 if invalid
                        end
                        return v
                    end

                    -- Initial wait before the first voltage read
                    sys.wait(interval)

                    -- Perform multiple read attempts to verify the voltage status
                    for i = 1, max_tries do
                        voltage = getVoltage()

                        if voltage <= voltage_threshold then
                            log.info("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV - Successful", i, voltage))
                            successful = true
                            break -- Exit the loop if voltage threshold is met
                        else
                            log.warn("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV above threshold (%.2fV)",
                                i, voltage, voltage_threshold))
                            if i < max_tries then
                                sys.wait(interval) -- Wait before the next attempt
                            end
                        end
                    end

                    -- After finishing attempts, read updated values for final SMS
                    local final_voltage = SensorModule.readVoltage() -- Get updated voltage after waiting
                    local temperature = SensorModule.readHumidityTemperature() -- Use actual temperature reading
                    local status = ""

                    -- Ensure final_voltage is valid and convert to number if needed
                    final_voltage = tonumber(final_voltage)
                    if not final_voltage then
                        log.error("Voltage", "Invalid voltage reading")
                        final_voltage = 0 -- Default to 0 if invalid
                    end

                    -- Determine the car status based on the updated final voltage logic
                    if final_voltage <= voltage_threshold then
                        status = successful and "untarlaa"
                    else
                        status = "check"
                    end

                    -- Format the message based on the updated readings
                    local message = string.format("Tani mashin:\nBatt: %.2fV | Temp: %.1fC\nStatus: %s", final_voltage,
                        temperature, status)

                    -- Send the formatted SMS after reading updated values only once
                    if vars.callback_number then
                        sms.send(vars.callback_number, message)
                    else
                        log.warn("SMS", "No callback number defined. Cannot send SMS.")
                    end

                elseif vars.sms_data == "as" then
                    -- Check if the device is licensed before executing command
                    if not vars.isLicensed then
                        -- If not licensed, send the license notification
                        sendLicenseNotify()
                    else
                        -- Execute the asCommand and handle any errors
                        local success, err = pcall(commands.asCommand)
                        if not success then
                            log.error("commands", "Failed to execute as command: " .. (err or "Unknown error"))
                        end
                    end

                    -- Parameters for multiple read attempts
                    local max_tries = 5 -- Maximum number of attempts
                    local interval = 4000 -- Interval between attempts in milliseconds (5 seconds)
                    local voltage_threshold = 13.4 -- Voltage threshold indicating successful start
                    local voltage = 0
                    local successful = false -- Flag to indicate success

                    -- Function to safely read and convert voltage to a number
                    local function getVoltage()
                        local v = SensorModule.readVoltage()
                        v = tonumber(v)
                        if not v then
                            log.error("Voltage", "Invalid voltage reading")
                            return 0 -- Default to 0 if invalid
                        end
                        return v
                    end

                    -- Initial wait before the first voltage read
                    sys.wait(interval)

                    -- Perform multiple read attempts to verify the voltage status
                    for i = 1, max_tries do
                        voltage = getVoltage()

                        if voltage >= voltage_threshold then
                            log.info("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV - Successful", i, voltage))
                            successful = true
                            break -- Exit the loop if voltage threshold is met
                        else
                            log.warn("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV below threshold (%.2fV)",
                                i, voltage, voltage_threshold))
                            if i < max_tries then
                                sys.wait(interval) -- Wait before the next attempt
                            end
                        end
                    end

                    -- After finishing attempts, read updated values for final SMS
                    local final_voltage = SensorModule.readVoltage() -- Get updated voltage after waiting
                    local temperature = SensorModule.readHumidityTemperature() -- Use actual temperature reading
                    local status = ""

                    -- Ensure final_voltage is valid and convert to number if needed
                    final_voltage = tonumber(final_voltage)
                    if not final_voltage then
                        log.error("Voltage", "Invalid voltage reading")
                        final_voltage = 0 -- Default to 0 if invalid
                    end

                    -- Determine the car status based on the updated final voltage logic
                    if final_voltage >= 13.4 then
                        status = "aslaa"
                    else
                        status = "tulhuur taniagvi"
                    end

                    -- Format the message based on the updated readings
                    local message = string.format("Tani mashin %s!\nBatt: %.2fV | Temp: %.1fC\nStatus: %s", status,
                        final_voltage, temperature, status)

                    -- Send the formatted SMS after reading updated values only once
                    if vars.callback_number then
                        sms.send(vars.callback_number, message)
                    else
                        log.warn("SMS", "No callback number defined. Cannot send SMS.")
                    end

                elseif vars.sms_data == "check" then
                    -- Check if the device is licensed before sending formatted data
                    if not vars.isLicensed then
                        -- If not licensed, send the license notification
                        sendLicenseNotify()
                    else
                        -- If licensed, send the formatted data
                        local formattedData = my_utils.packFormattedData()
                        sms.send(vars.callback_number, formattedData)
                    end
                elseif vars.sms_data == "lock" then
                    -- Check if the device is licensed before executing command
                    if not vars.isLicensed then
                        -- If not licensed, send the license notification
                        sendLicenseNotify()
                    else
                        local success, err = pcall(commands.lockCommand)
                        if not success then
                            log.error("commands", "Failed to execute lock command: " .. (err or "Unknown error"))
                        end
                        sms.send(vars.callback_number, "locked")
                    end

                elseif vars.sms_data == "unlock" then
                    -- Check if the device is licensed before executing command
                    if not vars.isLicensed then
                        -- If not licensed, send the license notification
                        sendLicenseNotify()
                    else
                        local success, err = pcall(commands.unlockCommand)
                        if not success then
                            log.error("commands", "Failed to execute unlock command: " .. (err or "Unknown error"))
                        end
                        sms.send(vars.callback_number, "unlocked")
                    end
                elseif vars.sms_data == "mirror" then
                    local success, err = pcall(commands.mirrorCommand)
                    if not success then
                        log.error("commands", "Failed to execute mirror command: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "mirror received")
                elseif vars.sms_data == "help" then
                    if vars.callback_number then
                        sms.send(vars.callback_number, "https://www.aslaa.mn/help")
                    else
                        log.warn("SMS", "No callback number defined. Cannot send help link.")
                    end
                elseif vars.sms_data == "update" then
                    update.setDownloadProcessCbFnc(function(percentage)
                        if LOG_LEVEL <= log.LOGLEVEL_INFO then
                            log.info("OTA", percentage .. "%...")
                        end
                    end)
                    update.request(function(success)
                        if success then
                            sms.send(vars.callback_number, "Update successful")
                            sys.wait(500)
                            local r = 1 -- Use the appropriate restart argument based on your platform
                            local success, err = pcall(function()
                                sys.restart(r)
                            end)
                            if not success then
                                log.error("commands", "failed to reboot: " .. (err or "Unknown error"))
                            end
                        else
                            local updateMsg = update.getUpdateMsg()
                            if updateMsg then
                                sms.send(vars.callback_number, "Update failed: " .. updateMsg)
                            else
                                sms.send(vars.callback_number, "Update failed: Unknown error")
                            end
                        end
                    end, nil, nil, true)
                elseif vars.sms_data == "version" then
                    sms.send(vars.callback_number, VERSION)
                elseif vars.sms_data == "sim" then
                    SmsModule.sendSms(1411, "see")
                    SmsModule.sendSms(vars.callback_number, "sim card info updated")
                elseif vars.sms_data == "id" then
                    sms.send(vars.callback_number, usermqtt.clientID())
                elseif vars.sms_data == "alarm off" then
                    vars.alarm_state = false
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Alarm state", vars.alarm_state)
                    end
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/alram.txt", vars.alarm_state)
                    if not success then
                        log.error("FileUtils", "Failed to write alarm state to file: " .. (err or "Unknown error"))
                    end
                elseif vars.sms_data == "alarm on" then
                    vars.alarm_state = "true"
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Alarm state", vars.alarm_state)
                    end
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/alram.txt", "true")
                    if not success then
                        log.error("FileUtils", "Failed to write alarm state to file: " .. (err or "Unknown error"))
                    end

                elseif vars.sms_data == "sound on" then
                    vars.sound_flag = true
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Sound Flag", "Sound flag set to True")
                    end
                    -- Write the sound_flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/sound_flag.txt",
                        tostring(vars.sound_flag))
                    if not success then
                        log.error("FileUtils", "Failed to write sound flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Speaker neegdlee")
                elseif vars.sms_data == "sound off" then
                    vars.sound_flag = false
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Sound Flag", "Sound flag set to False")
                    end
                    -- Write the sound_flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/sound_flag.txt",
                        tostring(vars.sound_flag))
                    if not success then
                        log.error("FileUtils", "Failed to write sound flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Speaker haagdsan")
                elseif vars.sms_data == "gps on" then
                    vars.gps_flag = true
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Gps Flag", "Gps flag set to True")
                    end
                    sms.send(vars.callback_number, "Gps enabled")
                elseif vars.sms_data == "gps off" then
                    vars.gps_flag = false
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Gps Flag", "Gps set to False")
                    end
                    sms.send(vars.callback_number, "Gps disabled")
                elseif vars.sms_data == "notify on" then
                    vars.voltage_notify_flag = true
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("VoltageMonitor", "Voltage notification flag set to True")
                    end
                    -- Write the voltage_notify_flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_notify.txt", tostring(vars.voltage_notify_flag))
                    if not success then
                        log.error("FileUtils", "Failed to write voltage notify flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Voltage notifications enabled")
                elseif vars.sms_data == "notify off" then
                    vars.voltage_notify_flag = false
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("VoltageMonitor", "Voltage notification flag set to False")
                    end
                    -- Write the voltage_notify_flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_notify.txt", tostring(vars.voltage_notify_flag))
                    if not success then
                        log.error("FileUtils", "Failed to write voltage notify flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Voltage notifications disabled")
                elseif vars.sms_data == "auto_shutdown on" then
                    vars.auto_shutdown_enabled = true
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("AutoShutdown", "Auto-shutdown feature enabled")
                    end
                    -- Write the auto_shutdown_enabled flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown.txt", tostring(vars.auto_shutdown_enabled))
                    if not success then
                        log.error("FileUtils", "Failed to write auto-shutdown flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Auto-shutdown feature enabled")
                elseif vars.sms_data == "auto_shutdown off" then
                    vars.auto_shutdown_enabled = false
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("AutoShutdown", "Auto-shutdown feature disabled")
                    end
                    -- Write the auto_shutdown_enabled flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown.txt", tostring(vars.auto_shutdown_enabled))
                    if not success then
                        log.error("FileUtils", "Failed to write auto-shutdown flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Auto-shutdown feature disabled")
                elseif vars.sms_data == "auto_shutdown status" then
                    -- Check the current auto-shutdown status
                    local status = vars.auto_shutdown_enabled and "enabled" or "disabled"
                    local timer_status = "inactive"

                    if vars.auto_shutdown_timer_id then
                        -- Calculate remaining time if timer is active
                        local elapsed_time = os.time() - vars.last_as_command_time
                        local total_time = vars.auto_shutdown_time / 1000  -- Convert from ms to seconds
                        local remaining_time = math.max(0, total_time - elapsed_time)
                        local remaining_minutes = math.floor(remaining_time / 60)

                        timer_status = string.format("active (shutdown in ~%d minutes)", remaining_minutes)
                    end

                    local message = string.format("Auto-shutdown feature is %s\nTimer duration: %d minutes\nTimer status: %s",
                        status, vars.auto_shutdown_minutes, timer_status)
                    sms.send(vars.callback_number, message)
                elseif vars.sms_data == "geely_atlas on" then
                    vars.geely_atlas_mode = true
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("VehicleMode", "Geely Atlas mode enabled")
                    end
                    -- Write the geely_atlas_mode flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/geely_atlas_mode.txt", tostring(vars.geely_atlas_mode))
                    if not success then
                        log.error("FileUtils", "Failed to write Geely Atlas mode flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Geely Atlas mode enabled")

                elseif vars.sms_data == "geely_atlas off" then
                    vars.geely_atlas_mode = false
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("VehicleMode", "Geely Atlas mode disabled")
                    end
                    -- Write the geely_atlas_mode flag to a file with error handling
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/geely_atlas_mode.txt", tostring(vars.geely_atlas_mode))
                    if not success then
                        log.error("FileUtils", "Failed to write Geely Atlas mode flag to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "Geely Atlas mode disabled")

                elseif vars.sms_data == "geely_atlas status" then
                    -- Check the current Geely Atlas mode status
                    local status = vars.geely_atlas_mode and "enabled" or "disabled"
                    local message = string.format("Geely Atlas mode is %s", status)
                    sms.send(vars.callback_number, message)

                elseif vars.sms_data:match("^auto_shutdown%s+timer%s+%d+$") then
                    -- Extract the minutes value from the command
                    local minutes = tonumber(vars.sms_data:match("auto_shutdown%s+timer%s+(%d+)"))

                    if minutes and minutes > 0 and minutes <= 120 then  -- Limit to reasonable values (up to 2 hours)
                        -- Update the auto-shutdown timer duration
                        vars.auto_shutdown_minutes = minutes
                        vars.auto_shutdown_time = vars.auto_shutdown_minutes * 60 * 1000  -- Convert minutes to milliseconds

                        -- Save to persistent storage
                        local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown_minutes.txt", tostring(vars.auto_shutdown_minutes))
                        if not success then
                            log.error("FileUtils", "Failed to write auto-shutdown minutes to file: " .. (err or "Unknown error"))
                        end

                        log.info("AutoShutdown", string.format("Auto-shutdown timer set to %d minutes", vars.auto_shutdown_minutes))
                        sms.send(vars.callback_number, string.format("Auto-shutdown timer set to %d minutes", vars.auto_shutdown_minutes))

                        -- If there's an active timer, update it with the new duration
                        if vars.auto_shutdown_timer_id then
                            -- Cancel the existing timer
                            sys.timerStop(vars.auto_shutdown_timer_id)
                            vars.auto_shutdown_timer_id = nil

                            -- Calculate elapsed time since the last 'as' command
                            local elapsed_time = os.time() - vars.last_as_command_time
                            local elapsed_ms = elapsed_time * 1000

                            -- Only restart the timer if we haven't exceeded the new duration
                            if elapsed_ms < vars.auto_shutdown_time then
                                -- Calculate remaining time
                                local remaining_time = vars.auto_shutdown_time - elapsed_ms

                                -- Set up a new timer with the remaining time
                                vars.auto_shutdown_timer_id = sys.timerStart(function()
                                    -- Use the same auto-shutdown logic as in asCommand
                                    local voltage = SensorModule.readVoltage()
                                    voltage = tonumber(voltage) or 0

                                    if voltage >= 13.5 then
                                        log.info("AutoShutdown", "Auto-shutdown triggered. Current voltage: " .. voltage)

                                        -- Execute the untarCommand
                                        local success, err = pcall(commands.untarCommand, 2000)
                                        if not success then
                                            log.error("AutoShutdown", "Failed to execute untar command: " .. (err or "Unknown error"))
                                        end

                                        -- Send MQTT notification
                                        local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                                            '{"status":"Auto-shutdown executed"}', 0)
                                        if not success then
                                            log.error("usermqtt", "Failed to send auto-shutdown notification: " .. (err or "Unknown error"))
                                        end
                                    else
                                        log.info("AutoShutdown", "Auto-shutdown canceled - car appears to be off already. Voltage: " .. voltage)
                                    end

                                    vars.auto_shutdown_timer_id = nil
                                end, remaining_time)

                                log.info("AutoShutdown", "Updated active timer with new duration")
                            else
                                log.info("AutoShutdown", "Timer not restarted as new duration has already elapsed")
                            end
                        end
                    else
                        sms.send(vars.callback_number, "Invalid timer value. Please use a number between 1 and 120 minutes.")
                    end
                elseif vars.sms_data == "logs" then
                    -- Send logs via SMS
                    log.info("SMS", "Received request for logs")
                    local success, err = pcall(LogModule.sendLogsViaSms, vars.callback_number, 5)  -- Send last 5 logs (SMS has size limitations)
                    if not success then
                        log.error("LogModule", "Failed to send logs via SMS: " .. (err or "Unknown error"))
                        sms.send(vars.callback_number, "Failed to retrieve logs")
                    end
                elseif vars.sms_data == "diagnostics" then
                    -- Send system diagnostics via SMS
                    log.info("SMS", "Received request for system diagnostics")
                    local diagnostics = LogModule.getFormattedDiagnostics()

                    -- Truncate if too long for SMS
                    if #diagnostics > 160 then
                        diagnostics = string.sub(diagnostics, 1, 157) .. "..."
                    end

                    local success, err = pcall(sms.send, vars.callback_number, diagnostics)
                    if not success then
                        log.error("LogModule", "Failed to send diagnostics via SMS: " .. (err or "Unknown error"))
                        sms.send(vars.callback_number, "Failed to retrieve diagnostics")
                    end
                elseif vars.sms_data == "clear_logs" then
                    -- Clear the log buffer
                    log.info("SMS", "Received request to clear logs")
                    local success, err = pcall(LogModule.clearLogs)
                    if not success then
                        log.error("LogModule", "Failed to clear logs: " .. (err or "Unknown error"))
                        sms.send(vars.callback_number, "Failed to clear logs")
                    else
                        sms.send(vars.callback_number, "Logs cleared successfully")
                    end
                elseif vars.sms_data:match("^log_level%s+%w+$") then
                    -- Handle all log level commands with a single block to reduce code duplication
                    local level_name = vars.sms_data:match("log_level%s+(%w+)")
                    local level_value

                    -- Set the appropriate log level based on the command
                    if level_name == "debug" then
                        level_value = log.LOGLEVEL_DEBUG
                    elseif level_name == "info" then
                        level_value = log.LOGLEVEL_INFO
                    elseif level_name == "warn" then
                        level_value = log.LOGLEVEL_WARN
                    elseif level_name == "error" then
                        level_value = log.LOGLEVEL_ERROR
                    end

                    -- Apply the log level change if we got a valid level
                    if level_value then
                        -- Use pcall to handle any potential errors
                        local success, err = pcall(function()
                            vars.log_level = level_value
                            -- Check if log.setLevel exists before calling it
                            if log.setLevel and type(log.setLevel) == "function" then
                                log.setLevel(level_value)
                            else
                                -- On AIR720U, we might need to set the log level differently
                                -- or just store it in vars.log_level for our own use
                                LOG_LEVEL = level_value -- Update the global variable
                            end
                            log.info("LogModule", "Log level set to " .. string.upper(level_name))
                        end)

                        if not success then
                            print("Error setting log level: " .. (err or "Unknown error"))
                            sms.send(vars.callback_number, "Error setting log level")
                        else
                            sms.send(vars.callback_number, "Log level set to " .. string.upper(level_name))
                        end
                    else
                        sms.send(vars.callback_number, "Invalid log level. Use debug, info, warn, or error.")
                    end
                elseif vars.sms_data == "license status" then
                    -- Check the current license status
                    local status = vars.isLicensed and "Licensed" or "Not licensed"
                    sms.send(vars.callback_number, "License status: " .. status)
                elseif vars.sms_data == "license enable" then
                    -- Enable the license
                    vars.isLicensed = true
                    log.info("License", "Device license enabled via SMS")
                    -- Save the license status to persistent storage
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/license.txt", tostring(vars.isLicensed))
                    if not success then
                        log.error("FileUtils", "Failed to write license status to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "License enabled")
                elseif vars.sms_data == "license disable" then
                    -- Disable the license
                    vars.isLicensed = false
                    log.info("License", "Device license disabled via SMS")
                    -- Save the license status to persistent storage
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/license.txt", tostring(vars.isLicensed))
                    if not success then
                        log.error("FileUtils", "Failed to write license status to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "License disabled")
                elseif vars.sms_data == "key off" then
                    vars.key_state = false
                    local success, err = pcall(pmd.ldoset, 0, 5)
                    if not success then
                        log.error("PMD", "Failed to disable key state: " .. (err or "Unknown error"))
                    end
                    -- Disable Go3 pin
                    local success, err = pcall(PinModule.relayControl, "Go3", 0)
                    if not success then
                    log.error("PinModule", "Failed to disable Go3: " .. (err or "Unknown error"))
                    end
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Key state", tostring(vars.key_state))
                    end
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/keystate.txt", tostring(vars.key_state))
                    if not success then
                        log.error("FileUtils", "Failed to write key state to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "spare key disabled")
                elseif vars.sms_data == "key on" then
                    vars.key_state = true
                    local success, err = pcall(pmd.ldoset, 7, 5)
                    if not success then
                        log.error("PMD", "Failed to enable key state: " .. (err or "Unknown error"))
                    end
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("Key state", tostring(vars.key_state))
                    end
                    -- Enable Go3 pin
                    local success, err = pcall(PinModule.relayControl, "Go3", 1)
                    if not success then
                    log.error("PinModule", "Failed to enable Go3: " .. (err or "Unknown error"))
                    end
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/keystate.txt", tostring(vars.key_state))
                    if not success then
                        log.error("FileUtils", "Failed to write key state to file: " .. (err or "Unknown error"))
                    end
                    sms.send(vars.callback_number, "spare key enabled")
                elseif vars.sms_data == "key status" then
                    -- Check the current key state
                    local status = vars.key_state and "enabled" or "disabled"
                    sms.send(vars.callback_number, "Key state is " .. status)
                elseif vars.sms_data == "mqtt_status" then
                    -- Check MQTT connection status using the helper function
                    log.info("MQTT", "Checking MQTT connection status via SMS")
                    local diagnostic_info = my_utils.checkMqttStatus()

                    -- Format diagnostic message for SMS
                    local message = string.format(
                        "MQTT Status:\nConnected: %s\nNetwork: %s\nSignal: %s\nError: %s\nMemory: %.1fKB",
                        tostring(diagnostic_info.mqtt_connected),
                        tostring(diagnostic_info.network_status),
                        tostring(diagnostic_info.signal_strength),
                        diagnostic_info.mqtt_error,
                        diagnostic_info.memory_free
                    )

                    -- Send diagnostic info via SMS
                    sms.send(vars.callback_number, message)
                elseif vars.sms_data == "mqtt_reconnect" then
                    -- Force MQTT reconnection
                    log.info("MQTT", "Forcing MQTT reconnection via SMS")

                    -- First, check current status
                    local status_before = my_utils.checkMqttStatus()
                    log.info("MQTT", "Status before reconnection - Connected: " .. tostring(status_before.mqtt_connected))

                    -- Try to disconnect first if connected
                    if usermqtt and usermqtt.disconnect then
                        local success, err = pcall(usermqtt.disconnect)
                        if success then
                            log.info("MQTT", "Successfully disconnected from MQTT broker")
                        else
                            log.error("MQTT", "Failed to disconnect from MQTT broker: " .. (err or "Unknown error"))
                        end
                    end

                    -- Wait a moment before reconnecting
                    sys.wait(2000)

                    -- Try to reconnect
                    if usermqtt and usermqtt.connect then
                        local success, err = pcall(usermqtt.connect)
                        if success then
                            log.info("MQTT", "Successfully initiated MQTT reconnection")
                        else
                            log.error("MQTT", "Failed to initiate MQTT reconnection: " .. (err or "Unknown error"))
                        end
                    end

                    -- Wait for connection to establish
                    sys.wait(5000)

                    -- Check status after reconnection
                    local status_after = my_utils.checkMqttStatus()
                    log.info("MQTT", "Status after reconnection - Connected: " .. tostring(status_after.mqtt_connected))

                    -- Format response message
                    local message = string.format(
                        "MQTT Reconnection:\nBefore: %s\nAfter: %s\nNetwork: %s\nSignal: %s",
                        tostring(status_before.mqtt_connected),
                        tostring(status_after.mqtt_connected),
                        tostring(status_after.network_status),
                        tostring(status_after.signal_strength)
                    )

                    -- Send response via SMS
                    sms.send(vars.callback_number, message)
                elseif vars.sms_data:sub(1, 3) == "asa" then
                    -- Check if the device is licensed before executing command
                    if not vars.isLicensed then
                        -- If not licensed, send the license notification
                        sendLicenseNotify()
                    else
                        local delay_value = tonumber(vars.sms_data:sub(4)) or 2 -- Default to 5 seconds if no value or invalid

                        -- Convert to milliseconds
                        local delay_ms = delay_value * 1000

                        -- Execute the asaCommand
                        local success, err = pcall(function()
                            commands.asaCommand(delay_ms)
                        end)
                        if not success then
                            log.error("commands", "Failed to execute asa command: " .. (err or "Unknown error"))
                        end

                        -- Wait for the specified delay before reading the updated values
                        sys.wait(delay_ms)

                        -- After the wait, read the voltage (use your method to read the updated battery voltage)
                        local voltage = SensorModule.readVoltage() -- Get updated voltage after waiting
                        local temperature = SensorModule.readHumidityTemperature() -- Get actual temperature reading from sensor
                        local status = ""

                        -- Ensure voltage is valid and convert to number if needed
                        voltage = tonumber(voltage)
                        if not voltage then
                            log.error("Voltage", "Invalid voltage reading")
                            voltage = 0 -- Default to 0 if invalid
                        end

                        -- Determine the car status based on the updated voltage logic
                        if voltage >= 13.5 then
                            status = "aslaa"
                        else
                            status = "check"
                        end

                        -- Format the message based on the new readings
                        local message = string.format("Tani mashin %s!\nBatt: %.2fV | Temp: %.1fC\nStatus: %s", status,
                            voltage, temperature, status)

                        -- Send the formatted SMS after reading updated values
                        sms.send(vars.callback_number, message)
                    end
                elseif vars.sms_data == "unt" then
                    -- Check if the device is licensed before executing command
                    if not vars.isLicensed then
                        -- If not licensed, send the license notification
                        sendLicenseNotify()
                    else
                        vars.relay1_state = 0
                        local success, err = pcall(PinModule.relayControl, "Relay1", 0)
                        if not success then
                            log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
                        end
                        if LOG_LEVEL <= log.LOGLEVEL_INFO then
                            log.info("RELAY1", "OFF")
                        end
                        sys.wait(5000) -- Wait for 5,000 milliseconds (5 seconds)
                        local voltage = SensorModule.readVoltage() -- Get updated voltage after waiting
                        local temperature = SensorModule.readHumidityTemperature() -- Use actual temperature reading
                        local status = ""
                        voltage = tonumber(voltage)
                        if not voltage then
                            log.error("Voltage", "Invalid voltage reading")
                            voltage = 0 -- Default to 0 if invalid
                        end
                        if voltage >= 13.5 then
                            status = "check"
                        else
                            status = "untarlaa"
                        end
                        local message = string.format("Tani mashin %s!\nBatt: %.2fV | Temp: %.1fC\nStatus: %s", status,
                            voltage, temperature, status)
                        sms.send(vars.callback_number, message)
                    end

                elseif string.find(string.lower(vars.sms_data), "set") then
                    -- Handle "SETMQTT serverX" command
                    local server_key = vars.sms_data:match("^set%s+(%w+)")
                    if server_key then
                        server_key = string.lower(server_key)
                        if server_key == "server1" or server_key == "server2" or server_key == "server3" then
                            -- Execute the server switch
                            local success, response = pcall(commands.handleSetMqtt, server_key)
                            if not success then
                                log.error("usermqtt", "Failed to set MQTT server: " .. (response or "Unknown error"))
                                sms.send(vars.callback_number, "Error: Failed to set server.")
                            else
                                sms.send(vars.callback_number, "set server success")

                                log.info("usermqtt", "MQTT server set to " .. server_key .. " successfully.")
                            end
                        else
                            sms.send(vars.callback_number, "Invalid server key. Use server1, server2, or server3.")
                            log.warn("usermqtt", "Received invalid server key: " .. server_key)
                        end
                    else
                        sms.send(vars.callback_number, "Invalid format.")
                        log.warn("usermqtt", "SETMQTT command received with missing server key.")
                    end
                elseif vars.sms_data:sub(1, 5) == "time:" then
                    -- Format: time:param_name:value_ms
                    -- Example: time:lock_press:1500
                    local parts = {}
                    for part in string.gmatch(vars.sms_data, "[^:]+") do
                        table.insert(parts, part)
                    end

                    if #parts == 3 then
                        local param_name = parts[2]
                        local value_ms = tonumber(parts[3])

                        local success, message = commands.setTimingParameter(param_name, value_ms)

                        -- Send confirmation SMS
                        sms.send(vars.callback_number, message)
                    else
                        -- Send error message
                        sms.send(vars.callback_number, "Invalid timing command format. Use time:param_name:value_ms")
                    end
                else
                    -- Handle unexpected commands by sending back "buruu"
                    if LOG_LEVEL <= log.LOGLEVEL_WARN then
                        log.warn("SMS Command", "Received unrecognized command: " .. tostring(vars.sms_data))
                    end
                    sms.send(vars.callback_number, "command jijgeer ardaa zai avaagvi baih!")
                end
            end
            vars.sms_data = nil
        end -- please only modify here for copy paste

        if usermqtt.read_msg() ~= nil then
            local msg = usermqtt.read_msg()
            processMqttMessage(msg)
            usermqtt.clear_msg()
        end

        if vars.mqtt_data ~= nil then
            if LOG_LEVEL <= log.LOGLEVEL_INFO then
                log.info("mqtt data", vars.mqtt_data)
            end

            if vars.mqtt_data == "on1" then
                vars.relay1_state = 1
                local success, err = pcall(PinModule.relayControl, "Relay1", 1)
                if not success then
                    log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
                end
                local success, err = pcall(pmd.ldoset, 7, 5)
                if not success then
                    log.error("PMD", "Failed to set LDO: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "off1" then
                vars.relay1_state = 0
                local success, err = pcall(PinModule.relayControl, "Relay1", 0)
                if not success then
                    log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "on2" then
                vars.relay2_state = 1
                local success, err = pcall(PinModule.relayControl, "Relay2", 1)
                if not success then
                    log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "off2" then
                vars.relay2_state = 0
                local success, err = pcall(PinModule.relayControl, "Relay2", 0)
                if not success then
                    log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "untar" then
                -- Execute the untarCommand with a 3-second wait
                local success, err = pcall(commands.untarCommand, 3000)
                if not success then
                    log.error("commands", "Failed to execute untar command: " .. (err or "Unknown error"))
                end

                -- Parameters for multiple read attempts
                local max_tries = 5 -- Maximum number of attempts
                local interval = 3000 -- Interval between attempts (ms)
                local voltage_threshold = 13.5
                local voltage = 0
                local successful = false

                local function getVoltage()
                    local v = SensorModule.readVoltage()
                    v = tonumber(v)
                    if not v then
                        log.error("Voltage", "Invalid voltage reading")
                        return 0
                    end
                    return v
                end

                -- Initial wait before the first voltage read
                sys.wait(interval)

                -- Perform multiple read attempts to verify the voltage status
                for i = 1, max_tries do
                    voltage = getVoltage()
                    if voltage <= voltage_threshold then
                        log.info("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV - Successful", i, voltage))
                        successful = true
                        break
                    else
                        log.warn("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV above threshold (%.2fV)", i,
                            voltage, voltage_threshold))
                        if i < max_tries then
                            sys.wait(interval)
                        end
                    end
                end

                -- Insert one final MQTT message
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "as" then
                -- Attempt to execute the asCommand function safely
                local success, err = pcall(commands.asCommand)
                if not success then
                    log.error("commands", "Failed to execute as command: " .. (err or "Unknown error"))
                end

                -- Parameters
                local max_tries = 5 -- Maximum number of attempts
                local interval = 4000 -- Interval between attempts in milliseconds (3 seconds)
                local voltage_threshold = 13.4 -- Voltage threshold indicating successful start

                local function getVoltage()
                    local v = SensorModule.readVoltage()
                    v = tonumber(v)
                    if not v then
                        log.error("Voltage", "Invalid voltage reading")
                        return 0 -- Default to 0 if invalid
                    end
                    return v
                end

                -- Optional: Give some delay before we start measuring (letting asCommand do its work)
                sys.wait(interval)

                local voltage
                local successStart = false

                for i = 1, max_tries do
                    voltage = getVoltage()

                    if voltage >= voltage_threshold then
                        log.info("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV - Successful", i, voltage))
                        successStart = true
                        break -- no need to keep checking
                    else
                        log.warn("VoltageCheck", string.format("Attempt %d: Voltage=%.2fV < threshold (%.2fV)", i,
                            voltage, voltage_threshold))
                        -- If we still have more attempts, wait again
                        if i < max_tries then
                            sys.wait(interval)
                        end
                    end
                end

                -- After finishing attempts, do one final MQTT insert
                -- (You can add details about success/fail or final voltage in packJsonData if you wish.)
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

                if successStart then
                    log.info("asCommand", "Car likely started successfully.")
                else
                    log.warn("asCommand", "Car did not reach threshold voltage.")
                end

            elseif vars.mqtt_data:sub(1, 3) == "asa" then
                -- Extract the delay value from the mqtt_data (e.g., "asa05" -> 5000ms, "asa06" -> 6000ms)
                local delay_value = tonumber(vars.mqtt_data:sub(4)) or 2 -- Default to 5 seconds if no value or invalid

                -- Convert to milliseconds
                local delay_ms = delay_value * 1000

                -- Execute the asaCommand with the parsed delay
                local success, err = pcall(function()
                    commands.asaCommand(delay_ms)
                end)

                -- Handle errors during the execution of asaCommand
                if not success then
                    log.error("commands", "Failed to execute asa command: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "check" then
                local success, err = pcall(commands.checkCommand)
                if not success then
                    log.error("commands", "Failed to execute check command: " .. (err or "Unknown error"))
                end
                -- Use the safe MQTT send function
                local success, err = my_utils.safeMqttSend(usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "version" then
                -- Use the safe MQTT send function
                local success, err = my_utils.safeMqttSend(usermqtt.clientID() .. "/msg", VERSION, 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "sim" then
                sms.send(1411, "see")
            elseif vars.mqtt_data == "number" then
                sms.send(88392933, "car's number")
            elseif vars.mqtt_data == "license" then
                vars.isLicensed = false
                -- Save the license status to persistent storage
                local success, err = pcall(my_utils.writeToFile, "/user_dir/license.txt", tostring(vars.isLicensed))
                if not success then
                    log.error("FileUtils", "Failed to write license status to file: " .. (err or "Unknown error"))
                end
                sendLicenseNotify()
            elseif vars.mqtt_data == "charged" or vars.mqtt_data == "license_enable" then
                vars.isLicensed = true
                log.info("License", "Device license enabled")
                -- Save the license status to persistent storage
                local success, err = pcall(my_utils.writeToFile, "/user_dir/license.txt", tostring(vars.isLicensed))
                if not success then
                    log.error("FileUtils", "Failed to write license status to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"license_enabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send license acknowledgment: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "license_disable" then
                vars.isLicensed = false
                log.info("License", "Device license disabled")
                -- Save the license status to persistent storage
                local success, err = pcall(my_utils.writeToFile, "/user_dir/license.txt", tostring(vars.isLicensed))
                if not success then
                    log.error("FileUtils", "Failed to write license status to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"license_disabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send license acknowledgment: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "license_status" then
                -- Send the current license status
                local status = vars.isLicensed and "licensed" or "not_licensed"
                -- Use the safe MQTT send function
                local success, err = my_utils.safeMqttSend(usermqtt.clientID() .. "/msg", '{"license_status":"' .. status .. '"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send license status: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "unlock" then
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

                local success, err = pcall(commands.unlockCommand)
                if not success then
                    log.error("commands", "Failed to execute unlock command: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "lock" then
                local success, err = pcall(commands.lockCommand)
                if not success then
                    log.error("commands", "Failed to execute lock command: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "mirror" then
                local success, err = pcall(commands.mirrorCommand)
                if not success then
                    log.error("commands", "Failed to execute mirror command: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "restart" then
                -- Correctly pass the function to pcall without invoking it
                local r = 1 -- Use the appropriate restart argument based on your platform
                local success, err = pcall(function()
                    sys.restart(r)
                end)
                if not success then
                    log.error("commands", "failed to reboot: " .. (err or "Unknown error"))
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "update" then
                update.setDownloadProcessCbFnc(function(percentage)
                    if LOG_LEVEL <= log.LOGLEVEL_INFO then
                        log.info("OTA", percentage .. "%...")
                    end
                end)
                update.request(function(success)
                    local message
                    if success then
                        message = '{"status": "Update successful"}'
                    else
                        local updateMsg = update.getUpdateMsg()
                        if updateMsg then
                            message = '{"status": "Update failed", "error": "' .. updateMsg .. '"}'
                        else
                            message = '{"status": "Update failed", "error": "Unknown error"}'
                        end
                    end
                    local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", message, 0)
                    if not success then
                        log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                    end
                end, nil, nil, true)
            elseif vars.mqtt_data == "key on" then
                vars.key_state = true
                local success, err = pcall(pmd.ldoset, 7, 5)
                if not success then
                    log.error("PMD", "Failed to enable key state: " .. (err or "Unknown error"))
                end
                -- Enable Go3 pin
                local success, err = pcall(PinModule.relayControl, "Go3", 1)
                if not success then
                    log.error("PinModule", "Failed to enable Go3: " .. (err or "Unknown error"))
                end
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("Key state", tostring(vars.key_state))
                end
                local success, err = pcall(my_utils.writeToFile, "/user_dir/keystate.txt", tostring(vars.key_state))
                if not success then
                    log.error("FileUtils", "Failed to write key state to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                    '{"status":"Key state enabled", "key_state":"on"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "key off" then
                vars.key_state = false
                local success, err = pcall(pmd.ldoset, 0, 5)
                if not success then
                    log.error("PMD", "Failed to disable key state: " .. (err or "Unknown error"))
                end
                -- Disable Go3 pin
                local success, err = pcall(PinModule.relayControl, "Go3", 0)
                if not success then
                    log.error("PinModule", "Failed to disable Go3: " .. (err or "Unknown error"))
                end
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("Key state", tostring(vars.key_state))
                end
                local success, err = pcall(my_utils.writeToFile, "/user_dir/keystate.txt", tostring(vars.key_state))
                if not success then
                    log.error("FileUtils", "Failed to write key state to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                    '{"status":"Key state disabled", "key_state":"off"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "key status" then
                -- Check the current key state
                local status = vars.key_state and "enabled" or "disabled"
                local key_value = vars.key_state and "on" or "off"
                -- Send status via MQTT
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                    string.format('{"status":"Key state is %s", "key_state":"%s"}', status, key_value), 0)
                if not success then
                    log.error("usermqtt", "Failed to send key state status: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "mqtt_status" then
                -- Check MQTT connection status using the helper function
                log.info("MQTT", "Checking MQTT connection status")
                local diagnostic_info = my_utils.checkMqttStatus()

                -- Log the diagnostic info
                log.info("MQTT Diagnostics", "Connected: " .. tostring(diagnostic_info.mqtt_connected) ..
                    ", Network: " .. tostring(diagnostic_info.network_status) ..
                    ", Signal: " .. tostring(diagnostic_info.signal_strength))

                -- Try to send diagnostic info via MQTT
                local json_data = string.format(
                    '{"status":"MQTT Diagnostics", "connected":%s, "network":"%s", "signal":"%s", "client_id":"%s", "uptime":%d, "memory_free":%d, "last_error":"%s"}',
                    tostring(diagnostic_info.mqtt_connected):lower(),
                    tostring(diagnostic_info.network_status),
                    tostring(diagnostic_info.signal_strength),
                    diagnostic_info.client_id,
                    diagnostic_info.uptime,
                    diagnostic_info.memory_free,
                    diagnostic_info.mqtt_error
                )

                -- Use the safe MQTT send function
                local success, err = my_utils.safeMqttSend(usermqtt.clientID() .. "/diagnostics", json_data, 0, 3, 1000)

                if not success then
                    log.error("MQTT", "Failed to send diagnostic info: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "mqtt_reconnect" then
                -- Force MQTT reconnection
                log.info("MQTT", "Forcing MQTT reconnection")

                -- First, check current status
                local status_before = my_utils.checkMqttStatus()
                log.info("MQTT", "Status before reconnection - Connected: " .. tostring(status_before.mqtt_connected))

                -- Try to disconnect first if connected
                if usermqtt and usermqtt.disconnect then
                    local success, err = pcall(usermqtt.disconnect)
                    if success then
                        log.info("MQTT", "Successfully disconnected from MQTT broker")
                    else
                        log.error("MQTT", "Failed to disconnect from MQTT broker: " .. (err or "Unknown error"))
                    end
                end

                -- Wait a moment before reconnecting
                sys.wait(2000)

                -- Try to reconnect
                if usermqtt and usermqtt.connect then
                    local success, err = pcall(usermqtt.connect)
                    if success then
                        log.info("MQTT", "Successfully initiated MQTT reconnection")
                    else
                        log.error("MQTT", "Failed to initiate MQTT reconnection: " .. (err or "Unknown error"))
                    end
                end

                -- Wait for connection to establish
                sys.wait(5000)

                -- Check status after reconnection
                local status_after = my_utils.checkMqttStatus()
                log.info("MQTT", "Status after reconnection - Connected: " .. tostring(status_after.mqtt_connected))

                -- Send status via MQTT
                local json_data = string.format(
                    '{"status":"MQTT Reconnection", "connected":%s, "network":"%s", "signal":"%s", "client_id":"%s"}',
                    tostring(status_after.mqtt_connected):lower(),
                    tostring(status_after.network_status),
                    tostring(status_after.signal_strength),
                    status_after.client_id
                )

                -- Use the safe MQTT send function
                local success, err = my_utils.safeMqttSend(usermqtt.clientID() .. "/diagnostics", json_data, 0, 3, 1000)

                if not success then
                    log.error("MQTT", "Failed to send reconnection status: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "sound on" then
                vars.sound_flag = true
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("MQTT", "Sound flag set to True")
                end
                -- Write the sound_flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/sound_flag.txt", tostring(vars.sound_flag))
                if not success then
                    log.error("FileUtils", "Failed to write sound flag to file: " .. (err or "Unknown error"))
                end
                -- Send MQTT acknowledgment
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "sound off" then
                vars.sound_flag = false
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("MQTT", "Sound flag set to False")
                end
                -- Write the sound_flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/sound_flag.txt", tostring(vars.sound_flag))
                if not success then
                    log.error("FileUtils", "Failed to write sound flag to file: " .. (err or "Unknown error"))
                end
                -- Send MQTT acknowledgment
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "notify on" then
                vars.voltage_notify_flag = true
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("VoltageMonitor", "Voltage notification flag set to True")
                end
                -- Write the voltage_notify_flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_notify.txt", tostring(vars.voltage_notify_flag))
                if not success then
                    log.error("FileUtils", "Failed to write voltage notify flag to file: " .. (err or "Unknown error"))
                end
                -- Send MQTT acknowledgment
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Voltage notifications enabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "notify off" then
                vars.voltage_notify_flag = false
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("VoltageMonitor", "Voltage notification flag set to False")
                end
                -- Write the voltage_notify_flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_notify.txt", tostring(vars.voltage_notify_flag))
                if not success then
                    log.error("FileUtils", "Failed to write voltage notify flag to file: " .. (err or "Unknown error"))
                end
                -- Send MQTT acknowledgment
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Voltage notifications disabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "get_logs" then
                -- Send logs via MQTT
                log.info("MQTT", "Received request for logs")
                local success, err = pcall(LogModule.sendLogsViaMqtt, 20)  -- Send last 20 logs
                if not success then
                    log.error("LogModule", "Failed to send logs via MQTT: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "get_error_logs" then
                -- Send only error logs via MQTT
                log.info("MQTT", "Received request for error logs")
                local success, err = pcall(LogModule.sendLogsViaMqtt, 50, "ERROR")  -- Send last 50 error logs
                if not success then
                    log.error("LogModule", "Failed to send error logs via MQTT: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "get_diagnostics" then
                -- Send system diagnostics via MQTT
                log.info("MQTT", "Received request for system diagnostics")
                local diagnostics = LogModule.getFormattedDiagnostics()
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/logs", diagnostics, 0)
                if not success then
                    log.error("LogModule", "Failed to send diagnostics via MQTT: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "clear_logs" then
                -- Clear the log buffer
                log.info("MQTT", "Received request to clear logs")
                local success, err = pcall(LogModule.clearLogs)
                if not success then
                    log.error("LogModule", "Failed to clear logs: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Logs cleared"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send acknowledgment: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "log_level debug" or vars.mqtt_data == "log_level info" or
                   vars.mqtt_data == "log_level warn" or vars.mqtt_data == "log_level error" then
                -- Handle all log level commands with a single block to reduce code duplication
                local level_name = vars.mqtt_data:match("log_level%s+(%w+)")
                local level_value

                -- Set the appropriate log level based on the command
                if level_name == "debug" then
                    level_value = log.LOGLEVEL_DEBUG
                elseif level_name == "info" then
                    level_value = log.LOGLEVEL_INFO
                elseif level_name == "warn" then
                    level_value = log.LOGLEVEL_WARN
                elseif level_name == "error" then
                    level_value = log.LOGLEVEL_ERROR
                end

                -- Apply the log level change if we got a valid level
                if level_value then
                    -- Use pcall to handle any potential errors
                    local success, err = pcall(function()
                        vars.log_level = level_value
                        -- Check if log.setLevel exists before calling it
                        if log.setLevel and type(log.setLevel) == "function" then
                            log.setLevel(level_value)
                        else
                            -- On AIR720U, we might need to set the log level differently
                            -- or just store it in vars.log_level for our own use
                            LOG_LEVEL = level_value -- Update the global variable
                        end
                        log.info("LogModule", "Log level set to " .. string.upper(level_name))
                    end)

                    if not success then
                        print("Error setting log level: " .. (err or "Unknown error"))
                    end

                    -- Send acknowledgment
                    local msg = string.format('{"status":"Log level set to %s"}', string.upper(level_name))
                    local ack_success, ack_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", msg, 0)
                    if not ack_success then
                        print("Failed to send acknowledgment: " .. (ack_err or "Unknown error"))
                    end
                end
            elseif vars.mqtt_data == "auto_shutdown on" then
                vars.auto_shutdown_enabled = true
                log.info("AutoShutdown", "Auto-shutdown feature enabled")
                -- Write the auto_shutdown_enabled flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown.txt", tostring(vars.auto_shutdown_enabled))
                if not success then
                    log.error("FileUtils", "Failed to write auto-shutdown flag to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Auto-shutdown feature enabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send acknowledgment: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "auto_shutdown off" then
                vars.auto_shutdown_enabled = false
                log.info("AutoShutdown", "Auto-shutdown feature disabled")
                -- Write the auto_shutdown_enabled flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown.txt", tostring(vars.auto_shutdown_enabled))
                if not success then
                    log.error("FileUtils", "Failed to write auto-shutdown flag to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Auto-shutdown feature disabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send acknowledgment: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "auto_shutdown status" then
                -- Check the current auto-shutdown status
                local status = vars.auto_shutdown_enabled and "enabled" or "disabled"
                local timer_status = "inactive"

                if vars.auto_shutdown_timer_id then
                    -- Calculate remaining time if timer is active
                    local elapsed_time = os.time() - vars.last_as_command_time
                    local total_time = vars.auto_shutdown_time / 1000  -- Convert from ms to seconds
                    local remaining_time = math.max(0, total_time - elapsed_time)
                    local remaining_minutes = math.floor(remaining_time / 60)

                    timer_status = string.format("active (shutdown in ~%d minutes)", remaining_minutes)
                end

                -- Send status via MQTT
                local message = string.format('{"auto_shutdown":"%s","timer_duration":%d,"timer_status":"%s"}',
                    status, vars.auto_shutdown_minutes, timer_status)
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", message, 0)
                if not success then
                    log.error("usermqtt", "Failed to send auto-shutdown status: " .. (err or "Unknown error"))
                end
            elseif vars.mqtt_data == "geely_atlas on" then
                vars.geely_atlas_mode = true
                log.info("VehicleMode", "Geely Atlas mode enabled")
                -- Write the geely_atlas_mode flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/geely_atlas_mode.txt", tostring(vars.geely_atlas_mode))
                if not success then
                    log.error("FileUtils", "Failed to write Geely Atlas mode flag to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Geely Atlas mode enabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send acknowledgment: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "geely_atlas off" then
                vars.geely_atlas_mode = false
                log.info("VehicleMode", "Geely Atlas mode disabled")
                -- Write the geely_atlas_mode flag to a file with error handling
                local success, err = pcall(my_utils.writeToFile, "/user_dir/geely_atlas_mode.txt", tostring(vars.geely_atlas_mode))
                if not success then
                    log.error("FileUtils", "Failed to write Geely Atlas mode flag to file: " .. (err or "Unknown error"))
                end
                -- Send acknowledgment
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", '{"status":"Geely Atlas mode disabled"}', 0)
                if not success then
                    log.error("usermqtt", "Failed to send acknowledgment: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data == "geely_atlas status" then
                -- Check the current Geely Atlas mode status
                local status = vars.geely_atlas_mode and "enabled" or "disabled"
                -- Send status via MQTT
                local message = string.format('{"geely_atlas_mode":"%s"}', status)
                local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", message, 0)
                if not success then
                    log.error("usermqtt", "Failed to send Geely Atlas mode status: " .. (err or "Unknown error"))
                end

            elseif vars.mqtt_data:match("^auto_shutdown%s+timer%s+%d+$") then
                -- Extract the minutes value from the command
                local minutes = tonumber(vars.mqtt_data:match("auto_shutdown%s+timer%s+(%d+)"))

                if minutes and minutes > 0 and minutes <= 120 then  -- Limit to reasonable values (up to 2 hours)
                    -- Update the auto-shutdown timer duration
                    vars.auto_shutdown_minutes = minutes
                    vars.auto_shutdown_time = vars.auto_shutdown_minutes * 60 * 1000  -- Convert minutes to milliseconds

                    -- Save to persistent storage
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/auto_shutdown_minutes.txt", tostring(vars.auto_shutdown_minutes))
                    if not success then
                        log.error("FileUtils", "Failed to write auto-shutdown minutes to file: " .. (err or "Unknown error"))
                    end

                    log.info("AutoShutdown", string.format("Auto-shutdown timer set to %d minutes", vars.auto_shutdown_minutes))

                    -- Send acknowledgment
                    local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                        string.format('{"status":"Auto-shutdown timer set to %d minutes"}', vars.auto_shutdown_minutes), 0)
                    if not success then
                        log.error("usermqtt", "Failed to send acknowledgment: " .. (err or "Unknown error"))
                    end

                    -- If there's an active timer, update it with the new duration
                    if vars.auto_shutdown_timer_id then
                        -- Cancel the existing timer
                        sys.timerStop(vars.auto_shutdown_timer_id)
                        vars.auto_shutdown_timer_id = nil

                        -- Calculate elapsed time since the last 'as' command
                        local elapsed_time = os.time() - vars.last_as_command_time
                        local elapsed_ms = elapsed_time * 1000

                        -- Only restart the timer if we haven't exceeded the new duration
                        if elapsed_ms < vars.auto_shutdown_time then
                            -- Calculate remaining time
                            local remaining_time = vars.auto_shutdown_time - elapsed_ms

                            -- Set up a new timer with the remaining time
                            vars.auto_shutdown_timer_id = sys.timerStart(function()
                                -- Use the same auto-shutdown logic as in asCommand
                                local voltage = SensorModule.readVoltage()
                                voltage = tonumber(voltage) or 0

                                if voltage >= 13.5 then
                                    log.info("AutoShutdown", "Auto-shutdown triggered. Current voltage: " .. voltage)

                                    -- Execute the untarCommand
                                    local success, err = pcall(commands.untarCommand, 2000)
                                    if not success then
                                        log.error("AutoShutdown", "Failed to execute untar command: " .. (err or "Unknown error"))
                                    end

                                    -- Send MQTT notification
                                    local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                                        '{"status":"Auto-shutdown executed"}', 0)
                                    if not success then
                                        log.error("usermqtt", "Failed to send auto-shutdown notification: " .. (err or "Unknown error"))
                                    end
                                else
                                    log.info("AutoShutdown", "Auto-shutdown canceled - car appears to be off already. Voltage: " .. voltage)
                                end

                                vars.auto_shutdown_timer_id = nil
                            end, remaining_time)

                            log.info("AutoShutdown", "Updated active timer with new duration")
                        else
                            log.info("AutoShutdown", "Timer not restarted as new duration has already elapsed")
                        end
                    end
                else
                    -- Send error message
                    local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                        '{"error":"Invalid timer value. Please use a number between 1 and 120 minutes."}', 0)
                    if not success then
                        log.error("usermqtt", "Failed to send error message: " .. (err or "Unknown error"))
                    end
                end
            elseif vars.mqtt_data == "unt" then
                vars.relay1_state = 0
                local success, err = pcall(PinModule.relayControl, "Relay1", 0)
                if not success then
                    log.error("PinModule", "Failed to control relay: " .. (err or "Unknown error"))
                end
                if LOG_LEVEL <= log.LOGLEVEL_INFO then
                    log.info("RELAY2", "OFF")
                end
                local success, err =
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", my_utils.packJsonData(), 0)
                if not success then
                    log.error("usermqtt", "Failed to insert MQTT message: " .. (err or "Unknown error"))
                end
                            elseif string.match(vars.mqtt_data, "^unitel:%d%d%d%d%d%d%d%d %d+$") then
                -- Handle unitel command via MQTT (similar to SMS handler)
                local phoneNumber, unitAmount = string.match(vars.mqtt_data, "^unitel:(%d%d%d%d%d%d%d%d) (%d+)$")
                unitAmount = tonumber(unitAmount)
                
                if unitAmount and unitAmount <= 2000 then
                    -- Send the command to the unitel service
                    local success, err = pcall(sms.send, "1444", phoneNumber .. " " .. unitAmount)
                    if not success then
                        log.error("SMS", "Failed to send Unitel command: " .. (err or "Unknown error"))
                        -- Send failure notification via MQTT
                        pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                            '{"status":"Error", "message":"Failed to send Unitel command"}', 0)
                    else
                        log.info("SMS", "Sent Unitel command: " .. phoneNumber .. " " .. unitAmount)
                        -- Send success notification via MQTT
                        pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                            string.format('{"status":"Success", "message":"Sent Unitel command for %s with %s units"}', phoneNumber, unitAmount), 0)
                    end
                else
                    -- Send error for invalid amount
                    log.warn("SMS", "Invalid Unitel command: Phone number must be 8 digits, amount must be <= 2000")
                    pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                        '{"status":"Error", "message":"Invalid Unitel command: Phone number must be 8 digits, amount must be <= 2000"}', 0)
                end
            elseif vars.mqtt_data:sub(1, 4) == "volt" then
                -- Format should be "volt1.2" where 1.2 is the desired offset
                local offset = tonumber(vars.mqtt_data:sub(5))
                if offset then
                    vars.voltage_offset = offset
                    -- Save to persistent storage
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_offset.txt", tostring(offset))
                    if not success then
                        log.error("FileUtils", "Failed to save voltage offset: " .. (err or "Unknown error"))
                    end
                    -- Send confirmation MQTT message
                    local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                        string.format('{"status":"Voltage offset set to %.2f"}', offset), 0)
                    if not success then
                        log.error("usermqtt", "Failed to send confirmation: " .. (err or "Unknown error"))
                    end
                end
            elseif vars.mqtt_data:sub(1, 2) == "th" then
                -- Format should be "th0.6" where 0.6 is the desired threshold
                local threshold = tonumber(vars.mqtt_data:sub(3))
                if threshold then
                    vars.voltage_threshold = threshold
                    -- Save to persistent storage
                    local success, err = pcall(my_utils.writeToFile, "/user_dir/volt_threshold.txt", tostring(threshold))
                    if not success then
                        log.error("FileUtils", "Failed to save voltage threshold: " .. (err or "Unknown error"))
                    end
                    -- Send confirmation MQTT message
                    local success, err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                        string.format('{"status":"Voltage threshold set to %.2f"}', threshold), 0)
                    if not success then
                        log.error("usermqtt", "Failed to send confirmation: " .. (err or "Unknown error"))
                    end
                    log.info("VoltageMonitor", string.format("Threshold updated to %.2f", threshold))
                else
                    log.warn("VoltageMonitor", "Invalid threshold format received")
                end
            elseif vars.mqtt_data:sub(1, 5) == "time:" then
                -- Format: time:param_name:value_ms
                -- Example: time:lock_press:1500
                local parts = {}
                for part in string.gmatch(vars.mqtt_data, "[^:]+") do
                    table.insert(parts, part)
                end

                if #parts == 3 then
                    local param_name = parts[2]
                    local value_ms = tonumber(parts[3])

                    local success, message = commands.setTimingParameter(param_name, value_ms)

                    -- Send confirmation MQTT message
                    local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                        string.format('{"status":"%s"}', message), 0)
                    if not mqtt_success then
                        log.error("usermqtt", "Failed to send confirmation: " .. (mqtt_err or "Unknown error"))
                    end
                else
                    -- Send error message
                    local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg",
                        '{"error":"Invalid timing command format. Use time:param_name:value_ms"}', 0)
                    if not mqtt_success then
                        log.error("usermqtt", "Failed to send error message: " .. (mqtt_err or "Unknown error"))
                    end
                end
            elseif string.find(vars.mqtt_data, "^setserver%s+") then
                -- Format: 
                -- setserver server1 - Connect to server1
                -- setserver server1=domain.com:1883 - Update server1 entry and connect to it
                -- setserver domain.com:1883 - Connect to custom domain without saving as a server entry
                
                log.info("main", "Processing setserver command: " .. vars.mqtt_data)
                
                local command = vars.mqtt_data:match("^setserver%s+(.+)$")
                if command then
                    -- Check if it's updating a server entry (format: server1=domain.com:1883)
                    local serverKey, serverInfo = command:match("^(server%d)=(.+)$")
                    if serverKey and serverInfo then
                        local domain, port = serverInfo:match("([^:]+):(%d*)")
                        port = tonumber(port) or 1883 -- Default to 1883 if port is not specified
                        
                        log.info("main", "Updating server entry: " .. serverKey .. " to " .. domain .. ":" .. port)
                        
                        -- Update the server entry
                        local success, response = pcall(usermqtt.updateServerEntry, serverKey, domain, port)
                        if success and response then
                            -- Send confirmation before changing server
                            local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                                '{"status":"Success", "message":"Server entry updated and changing to ' .. serverKey .. '. Restarting in 3 seconds..."}', 0)
                            if not mqtt_success then
                                log.error("usermqtt", "Failed to send confirmation: " .. (mqtt_err or "Unknown error"))
                            end
                            
                            -- Connect to the updated server (in a separate task)
                            sys.taskInit(function()
                                -- Give time for the message to be sent
                                sys.wait(1000)
                                pcall(usermqtt.setMqttServer, serverKey)
                            end)
                        else
                            log.error("main", "Failed to update server entry: " .. (response or "Unknown error"))
                        end
                    -- Check if it's a predefined server key (format: server1)
                    elseif command == "server1" or command == "server2" or command == "server3" then
                        -- Send confirmation before changing server
                        local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                            '{"status":"Success", "message":"Changing to ' .. command .. '. Restarting in 3 seconds..."}', 0)
                        if not mqtt_success then
                            log.error("usermqtt", "Failed to send confirmation: " .. (mqtt_err or "Unknown error"))
                        end
                        
                        -- Connect to the server (in a separate task)
                        sys.taskInit(function()
                            -- Give time for the message to be sent
                            sys.wait(1000)
                            pcall(usermqtt.setMqttServer, command)
                        end)
                    else
                        -- Check if it's a custom domain:port format (format: domain.com:1883)
                        local domain, port = command:match("([^:]+):(%d*)")
                        port = tonumber(port) or 1883 -- Default to 1883 if port is not specified
                        
                        if domain and port > 0 and port < 65536 then
                            -- Send confirmation before changing server
                            local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                                '{"status":"Success", "message":"Changing to custom server ' .. domain .. ':' .. port .. '. Restarting in 3 seconds..."}', 0)
                            if not mqtt_success then
                                log.error("usermqtt", "Failed to send confirmation: " .. (mqtt_err or "Unknown error"))
                            end
                            
                            -- Connect to the custom server (in a separate task)
                            sys.taskInit(function()
                                -- Give time for the message to be sent
                                sys.wait(1000)
                                pcall(usermqtt.setMqttServer, nil, domain, port)
                            end)
                        else
                            -- Only send an error response for invalid format
                            local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                                '{"status":"Error", "message":"Invalid format. Use setserver server1, setserver server1=domain.com:1883, or setserver domain.com:1883"}', 0)
                            if not mqtt_success then
                                log.error("usermqtt", "Failed to send error message: " .. (mqtt_err or "Unknown error"))
                            end
                        end
                    end
                else
                    -- Send error for invalid format
                    local mqtt_success, mqtt_err = pcall(usermqtt.insertMsg, usermqtt.clientID() .. "/msg", 
                        '{"status":"Error", "message":"Invalid format. Use setserver server1, setserver server1=domain.com:1883, or setserver domain.com:1883"}', 0)
                    if not mqtt_success then
                        log.error("usermqtt", "Failed to send error message: " .. (mqtt_err or "Unknown error"))
                    end
                end
            end

            vars.mqtt_data = nil
        end

        sys.wait(100)
    end
end)

sys.init(0, 0)
sys.run()
